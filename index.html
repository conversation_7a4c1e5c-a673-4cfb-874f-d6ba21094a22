<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rive Animation Display</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(45deg, #000000 25%, #ffffff 25%, #ffffff 50%, #000000 50%, #000000 75%, #ffffff 75%);
            background-size: 40px 40px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
        }

        .container {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        #rive-canvas {
            border: 2px solid #333;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            background: white;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
        }

        .loading {
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Rive Animation</h1>
        <canvas id="rive-canvas" width="400" height="400"></canvas>
        <div class="loading" id="loading-text">Loading animation...</div>
    </div>

    <script src="https://unpkg.com/@rive-app/canvas@2.7.0"></script>
    <script>
        const canvas = document.getElementById('rive-canvas');
        const loadingText = document.getElementById('loading-text');

        // Initialize Rive
        const rive = new rive_canvas.Rive({
            src: './giaspinwell.riv',
            canvas: canvas,
            autoplay: true,
            onLoad: () => {
                console.log('Rive animation loaded successfully');
                loadingText.style.display = 'none';
                // Fit the animation to the canvas
                rive.resizeDrawingSurfaceToCanvas();
            },
            onLoadError: (error) => {
                console.error('Failed to load Rive animation:', error);
                loadingText.textContent = 'Failed to load animation';
                loadingText.style.color = 'red';
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (rive) {
                rive.resizeDrawingSurfaceToCanvas();
            }
        });

        // Optional: Add click interaction to play/pause
        canvas.addEventListener('click', () => {
            if (rive.isPlaying) {
                rive.pause();
            } else {
                rive.play();
            }
        });
    </script>
</body>
</html>

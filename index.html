<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rive Animation Display</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
            transition: background-color 0.5s ease;
            cursor: pointer;
        }

        body.black-theme {
            background-color: #000000;
        }

        body.white-theme {
            background-color: #ffffff;
        }

        .container {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            transition: all 0.5s ease;
        }

        .black-theme .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(255, 255, 255, 0.2);
        }

        .white-theme .container {
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        #rive-canvas {
            border-radius: 10px;
            transition: all 0.5s ease;
        }

        .black-theme #rive-canvas {
            border: 2px solid #ffffff;
            box-shadow: 0 4px 20px rgba(255, 255, 255, 0.3);
        }

        .white-theme #rive-canvas {
            border: 2px solid #000000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        h1 {
            margin-bottom: 20px;
            transition: color 0.5s ease;
        }

        .black-theme h1 {
            color: #ffffff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .white-theme h1 {
            color: #000000;
            text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.8);
        }

        .loading {
            font-style: italic;
            margin-top: 10px;
            transition: color 0.5s ease;
        }

        .black-theme .loading {
            color: #cccccc;
        }

        .white-theme .loading {
            color: #333333;
        }

        .click-instruction {
            margin-top: 15px;
            font-size: 14px;
            opacity: 0.8;
            transition: color 0.5s ease;
        }

        .black-theme .click-instruction {
            color: #cccccc;
        }

        .white-theme .click-instruction {
            color: #666666;
        }
    </style>
</head>
<body class="black-theme">
    <div class="container">
        <h1>Rive Animation - Switch Theme</h1>
        <canvas id="rive-canvas" width="400" height="400"></canvas>
        <div class="loading" id="loading-text">Loading animation...</div>
        <div class="click-instruction">Click anywhere to switch between black and white themes</div>
    </div>

    <script src="https://unpkg.com/@rive-app/canvas@2.7.0"></script>
    <script>
        const canvas = document.getElementById('rive-canvas');
        const loadingText = document.getElementById('loading-text');
        const body = document.body;

        let currentRive = null;
        let isBlackTheme = true;

        // Function to load Rive animation
        function loadRiveAnimation(src) {
            // Clean up previous animation
            if (currentRive) {
                currentRive.cleanup();
                currentRive = null;
            }

            loadingText.style.display = 'block';
            loadingText.textContent = 'Loading animation...';

            // Initialize new Rive animation
            currentRive = new rive_canvas.Rive({
                src: src,
                canvas: canvas,
                autoplay: true,
                onLoad: () => {
                    console.log('Rive animation loaded successfully:', src);
                    loadingText.style.display = 'none';
                    // Fit the animation to the canvas
                    currentRive.resizeDrawingSurfaceToCanvas();
                },
                onLoadError: (error) => {
                    console.error('Failed to load Rive animation:', error);
                    loadingText.textContent = 'Failed to load animation';
                    loadingText.style.color = 'red';
                }
            });
        }

        // Function to switch theme
        function switchTheme() {
            isBlackTheme = !isBlackTheme;

            if (isBlackTheme) {
                body.className = 'black-theme';
                // Load the same Rive file for black theme
                loadRiveAnimation('./giaspinwell.riv');
            } else {
                body.className = 'white-theme';
                // Load the same Rive file for white theme
                loadRiveAnimation('./giaspinwell.riv');
            }
        }

        // Initialize with black theme
        loadRiveAnimation('./giaspinwell.riv');

        // Handle window resize
        window.addEventListener('resize', () => {
            if (currentRive) {
                currentRive.resizeDrawingSurfaceToCanvas();
            }
        });

        // Add click interaction to switch theme
        body.addEventListener('click', (e) => {
            // Prevent switching when clicking on canvas for animation control
            if (e.target !== canvas) {
                switchTheme();
            }
        });

        // Canvas click for play/pause
        canvas.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent theme switching
            if (currentRive && currentRive.isPlaying) {
                currentRive.pause();
            } else if (currentRive) {
                currentRive.play();
            }
        });
    </script>
</body>
</html>
